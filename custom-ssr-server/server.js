const express = require('express');
const path = require('path');
const fs = require('fs');
const ManifestLoader = require('./manifest-loader');
const RouteHandler = require('./route-handler');
const PageRenderer = require('./page-renderer');

class CustomNextSSRServer {
  constructor(options = {}) {
    this.buildDir = options.buildDir || '.next';
    this.publicDir = options.publicDir || 'public';
    this.port = options.port || 3000;
    this.dev = options.dev || false;
    
    // 初始化组件
    this.manifestLoader = new ManifestLoader(this.buildDir);
    this.manifests = this.manifestLoader.loadAllManifests();
    this.routeHandler = new RouteHandler(this.manifests);
    this.pageRenderer = new PageRenderer(this.buildDir, this.manifests);
    
    // 创建 Express 应用
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    // 解析 JSON 和 URL 编码的请求体
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));

    // 静态文件服务
    this.app.use('/_next/static', express.static(path.join(this.buildDir, 'static')));
    this.app.use('/static', express.static(this.publicDir));
    
    // 开发模式下的热重载支持
    if (this.dev) {
      this.app.use('/_next/webpack-hmr', (req, res) => {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive'
        });
        res.write('data: {"action":"built","hash":"dev"}\n\n');
      });
    }

    // 请求日志
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
      next();
    });
  }

  setupRoutes() {
    // API 路由处理
    this.app.use('/api', this.handleAPIRoutes.bind(this));
    
    // 主路由处理器
    this.app.get('*', this.handlePageRequest.bind(this));
  }

  async handleAPIRoutes(req, res, next) {
    const apiPath = req.path.replace('/api', '');
    const routeInfo = this.routeHandler.getRouteInfo(`/api${apiPath}`);
    
    if (routeInfo.type === 'page') {
      try {
        const result = await this.pageRenderer.render(routeInfo, req, res);
        if (result && typeof result === 'object' && result.type === 'redirect') {
          return res.redirect(result.statusCode || 302, result.destination);
        }
        // API 路由通常直接操作 res 对象
        if (!res.headersSent) {
          res.status(404).json({ error: 'API route not found' });
        }
      } catch (error) {
        console.error('API route error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    } else {
      next();
    }
  }

  async handlePageRequest(req, res) {
    try {
      const routeInfo = this.routeHandler.getRouteInfo(req.path);
      console.log(routeInfo)
      switch (routeInfo.type) {
        case 'redirect':
          return res.redirect(routeInfo.statusCode, routeInfo.destination);
          
        case 'page':
          const html = await this.pageRenderer.render(routeInfo, req, res);
          
          if (html && typeof html === 'object') {
            if (html.type === 'redirect') {
              return res.redirect(html.statusCode || 302, html.destination);
            }
            if (html.type === 'notFound') {
              return this.handle404(req, res);
            }
          }
          
          res.setHeader('Content-Type', 'text/html; charset=utf-8');
          return res.send(html);
          
        case 'static':
          return this.handleStaticFile(req, res);
          
        default:
          return this.handle404(req, res);
      }
    } catch (error) {
      console.error('Request handling error:', error);
      return this.handle500(req, res, error);
    }
  }

  handleStaticFile(req, res) {
    const filePath = path.join(this.publicDir, req.path);
    
    if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
      return res.sendFile(path.resolve(filePath));
    }
    
    return this.handle404(req, res);
  }

  handle404(req, res) {
    // 尝试渲染自定义 404 页面
    const notFoundRoute = this.routeHandler.getRouteInfo('/404');
    if (notFoundRoute.type === 'page') {
      this.pageRenderer.render(notFoundRoute, req, res)
        .then(html => {
          res.status(404).setHeader('Content-Type', 'text/html; charset=utf-8');
          res.send(html);
        })
        .catch(() => {
          res.status(404).send('<h1>404 - Page Not Found</h1>');
        });
    } else {
      res.status(404).send('<h1>404 - Page Not Found</h1>');
    }
  }

  handle500(req, res, error) {
    // 尝试渲染自定义 500 页面
    const errorRoute = this.routeHandler.getRouteInfo('/500');
    if (errorRoute.type === 'page') {
      this.pageRenderer.render(errorRoute, req, res)
        .then(html => {
          res.status(500).setHeader('Content-Type', 'text/html; charset=utf-8');
          res.send(html);
        })
        .catch(() => {
          res.status(500).send(`<h1>500 - Internal Server Error</h1><pre>${error.stack}</pre>`);
        });
    } else {
      res.status(500).send(`<h1>500 - Internal Server Error</h1><pre>${error.stack}</pre>`);
    }
  }

  // 启动服务器
  listen(callback) {
    this.app.listen(this.port, (err) => {
      if (err) {
        console.error('Failed to start server:', err);
        return;
      }
      
      console.log(`🚀 Custom Next.js SSR Server running on http://localhost:${this.port}`);
      console.log(`📁 Build directory: ${this.buildDir}`);
      console.log(`📁 Public directory: ${this.publicDir}`);
      console.log(`🔧 Development mode: ${this.dev}`);
      
      if (callback) callback();
    });
  }

  // 优雅关闭
  close() {
    if (this.server) {
      this.server.close();
    }
  }
}

// 使用示例
if (require.main === module) {
  const server = new CustomNextSSRServer({
    buildDir: '.next',
    publicDir: 'public',
    port: 3000,
    dev: process.env.NODE_ENV !== 'production'
  });
  
  server.listen();
  
  // 优雅关闭处理
  process.on('SIGTERM', () => {
    console.log('Received SIGTERM, shutting down gracefully');
    server.close();
    process.exit(0);
  });
  
  process.on('SIGINT', () => {
    console.log('Received SIGINT, shutting down gracefully');
    server.close();
    process.exit(0);
  });
}

module.exports = CustomNextSSRServer;
